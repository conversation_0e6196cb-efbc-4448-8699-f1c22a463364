'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import Link from 'next/link';

import { Button } from '@/components/atoms/Button/Button';
import { FormField } from '@/components/molecules/FormField/FormField';
import { InputWithIcon } from '@/components/molecules/InputWithIcon/InputWithIcon';
import { CheckboxItem } from '@/components/molecules/CheckboxItem/CheckboxItem';
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Mail, Lock } from 'lucide-react';

import { LoginValues, loginSchema } from './SignInForm.schema';
import { cn } from '@/utils/cn';
import { Icon } from '@/components/atoms';

export default function SignInForm() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  const onSubmit = async (data: LoginValues) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await signIn('credentials', {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (response?.error) {
        setError(response.error);
        return;
      }

      // Add a small delay to ensure session is fully synchronized
      await new Promise(resolve => setTimeout(resolve, 300));

      // // Refresh the router to ensure layout re-renders with updated session
      // router.refresh();
      window.location.href = '/';

      // router.push('/'); // Redirect to dashboard on success
    } catch (err: any) {
      setError(err.message || 'An error occurred during sign in');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="space-y-6">
        {/* Error Alert */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <AlertMessage
              type="error"
              message={error}
            />
          </motion.div>
        )}

        {/* Main Sign In Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <div className="relative">
          {/* Gradient Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 rounded-2xl"></div>

          {/* Glass Effect Overlay */}
          <div className="relative bg-white/80 backdrop-blur-sm border border-white/20 rounded-2xl shadow-xl shadow-blue-500/10 p-6">
            {/* Decorative Elements */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full -mr-16 -mt-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-green-400/10 to-blue-400/10 rounded-full -ml-12 -mb-12"></div>

            {/* Logo and Header */}
            <div className="text-center mb-6 relative z-10">
              <motion.div
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <h1 className="text-xl font-semibold flex justify-center items-center gap-2 text-gray-800 mb-1">
                  Welcome to <Icon variant="logo" size={30} />
                </h1>
              </motion.div>
              <motion.div
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <p className="text-sm text-gray-600">
                  Sign in to access your AI teaching assistant
                </p>
              </motion.div>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 relative z-10" role="form" aria-label="Sign in form">
            {/* Enhanced Form Fields */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <FormField
                label="Email"
                error={errors.email?.message}
                required
              >
                <InputWithIcon
                  type="email"
                  placeholder="Enter your email address"
                  leftIcon={<Mail size={18} className="text-blue-500" />}
                  className={cn(
                    'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                    'bg-white/50 backdrop-blur-sm',
                    errors.email
                      ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                      : 'border-gray-200 focus:border-blue-500'
                  )}
                  aria-describedby={errors.email ? 'email-error' : undefined}
                  {...register('email')}
                />
              </FormField>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <FormField
                label="Password"
                error={errors.password?.message}
                required
              >
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <Lock size={18} className="text-blue-500" />
                  </div>
                  <PasswordInput
                    placeholder="Enter your password"
                    hasLeftIcon={true}
                    className={cn(
                      'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                      'bg-white/50 backdrop-blur-sm',
                      errors.password
                        ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                        : 'border-gray-200 focus:border-blue-500'
                    )}
                    aria-describedby={errors.password ? 'password-error' : undefined}
                    {...register('password')}
                  />
                </div>
              </FormField>
            </motion.div>

            {/* Form Actions */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <div className="flex items-center justify-between mb-4">
                <CheckboxItem
                  label="Remember Me"
                  {...register('rememberMe')}
                />
                <a
                  href="/auth/forgot-password"
                  className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md px-2 py-1"
                  aria-label="Reset your password"
                >
                  Forgot Password?
                </a>
              </div>

              <Button
                type="submit"
                variant="primary"
                className="w-full h-12 text-base font-semibold rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl focus:ring-4 focus:ring-blue-200 focus:ring-offset-2 border-0"
                isLoading={isLoading}
                disabled={isLoading}
                aria-label={isLoading ? 'Signing in...' : 'Sign in to your account'}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Signing In...</span>
                  </div>
                ) : (
                  'Sign In'
                )}
              </Button>
            </motion.div>
          </form>
          </div>
          </div>
        </motion.div>



        {/* Navigation to Sign Up */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <div className="text-center pt-6 border-t border-gray-200">
            <p className="text-gray-600">
              New to EduSG?{' '}
              <Link
                href="/auth/sign-up"
                className="text-blue-600 hover:text-blue-700 font-medium transition-colors"
              >
                Create your account
              </Link>
            </p>
          </div>
        </motion.div>

        {/* Enhanced Trust Indicators */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.9 }}
        >
          <div className="mt-6 text-center">
            <div className="flex flex-col sm:flex-row items-center justify-center gap-2 text-xs text-text-secondary">
              <div className="flex items-center space-x-2 bg-green-50 px-3 py-1.5 rounded-full border border-green-100">
                <div className="w-2 h-2 bg-gradient-to-r from-green-400 to-green-500 rounded-full"></div>
                <span className="font-medium">Trusted by 50,000+ students</span>
              </div>
              <div className="flex items-center space-x-2 bg-section-bg-accent px-3 py-1.5 rounded-full border border-blue-100">
                <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full"></div>
                <span className="font-medium">MOE-compliant platform</span>
              </div>
            </div>
          </div>
        </motion.div>
        </div>
      </motion.div>
    </div>
  );
}
