'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Icon from '@/components/atoms/Icon/Icon';
import { cn } from '@/utils/cn';


interface AuthTemplateProps {
  children: ReactNode;
  className?: string;
}

export default function AuthTemplate({ children, className }: AuthTemplateProps) {
  return (
    <div className="min-h-screen flex flex-col lg:flex-row relative overflow-x-hidden">
      <div className="hidden md:block absolute top-8 left-8 z-10">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex items-center space-x-3">
              <Icon variant="logo" size={52} className="drop-shadow-sm" />
            </div>
          </motion.div>
        </div>

      {/* Left Section - Illustration and Branding */}
      <div className="bg-section-bg-accent hidden lg:flex lg:w-1/2 p-8 flex-col justify-center items-center relative overflow-hidden space-y-8">
      
          {/* Main Illustration */}
          <div className="flex justify-center items-center z-10">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="max-w-lg w-full h-auto bg-white/10 rounded-2xl p-4 flex items-center justify-center relative">
                <Image
                  src="/assets/sign-in.png"
                  alt="Education Illustration"
                  width={500}
                  height={420}
                  className="w-full h-full object-contain rounded-xl"
                  onError={(e) => {
                    // Show fallback content
                    const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                    e.currentTarget.style.display = 'none';
                  }}
                  onLoad={() => console.log('Image loaded successfully')}
                />
                {/* Fallback content */}
                <div className="absolute inset-0 hidden flex-col items-center justify-center text-center p-8">
                  <div className="w-24 h-24 bg-gradient-to-br from-link-default to-button-pill-bg rounded-full flex items-center justify-center mb-4">
                    <Icon variant="book-open" size={8} className="text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">Education Platform</h3>
                  <p className="text-sm text-gray-600">Empowering Singapore&apos;s Future</p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Background Decorative Elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-interactive-hover-bg-light rounded-full -mr-32 -mt-16 opacity-50"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-interactive-hover-bg-light rounded-full -ml-32 -mb-16 opacity-50"></div>
    
      </div>

      {/* Right Section - Content */}
      <div className={cn(
        'lg:w-1/2 p-4 sm:p-6 lg:p-8 flex flex-col justify-center items-center min-h-screen overflow-y-auto',
        className
      )}>
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="w-full max-w-lg">
          {children}
        </div>
        </motion.div>
      </div>
    </div>
  );
}
