import type { Metadata } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import { CustomProvider } from '@/providers/CustomProvider';
import { onSeverSession } from '@/config/auth';
import DashboardTemplate from '@/components/templates/Dashboard/Dashboard';
import { ClipboardListIcon, HomeIcon, UsersIcon, UserIcon, Building2Icon as Building2, Settings } from 'lucide-react'; // Added UsersIcon, UserIcon, Building2, and Settings
import { headers } from 'next/headers'; // Import headers to get the current path
import { ERoutes } from '@/config/enums/enum';
import { EUserRole } from '@/config/enums/user'; // Added EUserRole import
import { getMySchool } from '@/actions/school.action'; // Import school action for INDEPENDENT_TEACHER
import { redirect } from 'next/navigation';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const ssrSession = await onSeverSession();

  // Get school information from session
  // Ensure schoolInfo is of type ISchoolResponse | null
  const schoolInfo = (ssrSession?.user?.schoolId && ssrSession?.user?.school) ? {
    id: ssrSession.user.schoolId, // Use schoolId from user object
    name: ssrSession.user.school.name,
    address: ssrSession.user.school.address || '',
    phoneNumber: ssrSession.user.school.phoneNumber || '',
    registeredNumber: ssrSession.user.school.registeredNumber || '',
    email: ssrSession.user.school.email || '',
    brand: ssrSession.user.school.brand, // brand from session.user.school
    // admin, createdAt, updatedAt are not in session.user.school, so they will be undefined.
    // This is acceptable as they are optional in ISchoolResponse.
  } : null;

  // Get the current pathname from headers
  const headerList = await headers();
  const currentPath = headerList.get('x-current-path') || '/';

  // Base sidebar items - will be different for independent teachers
  const baseSidebarItems = [];
  
  // For independent teachers, only show Manage Worksheet as the main item
  if (ssrSession?.user?.role === EUserRole.INDEPENDENT_TEACHER) {
    baseSidebarItems.push({
      label: 'Manage Worksheet',
      href: ERoutes.MANAGE_WORKSHEET,
      icon: <ClipboardListIcon className="w-5 h-5" />,
    });
  } else {
    // For all other roles, show Dashboard as the first item
    baseSidebarItems.push({
      label: 'Dashboard',
      href: ERoutes.HOME,
      icon: <HomeIcon className="w-5 h-5" />,
    });
  }

  // Conditionally add items based on user role
  const sidebarItems = [...baseSidebarItems];

  // Add "Manage Worksheet" for non-admin users who are not independent teachers
  if (ssrSession?.user?.role !== EUserRole.ADMIN && ssrSession?.user?.role !== EUserRole.INDEPENDENT_TEACHER) {
    sidebarItems.push({
      label: 'Manage Worksheet',
      href: ERoutes.MANAGE_WORKSHEET,
      icon: <ClipboardListIcon className="w-5 h-5" />,
    });
  }

  // Add "Users Management" only for admin users
  if (ssrSession?.user?.role === EUserRole.ADMIN) {
    sidebarItems.push({
      label: 'Users Management',
      href: ERoutes.USERS_MANAGEMENT,
      icon: <UsersIcon className="w-5 h-5" />,
    });
    sidebarItems.push({
      label: 'School Management',
      href: ERoutes.SCHOOLS,
      icon: <Building2 className="w-5 h-5" />,
    });
  }

  // Add "Teacher Management" only for school_manager users
  if (ssrSession?.user?.role === EUserRole.SCHOOL_MANAGER) {
    sidebarItems.push({
      label: 'Teacher Management',
      href: ERoutes.TEACHER_MANAGEMENT,
      icon: <UserIcon className="w-5 h-5" />,
    });
  }

  // Add school-related navigation for INDEPENDENT_TEACHER users
  if (ssrSession?.user?.role === EUserRole.INDEPENDENT_TEACHER) {
    try {
      // Check if the independent teacher has a school
      const schoolResponse = await getMySchool();

      if (schoolResponse.status === 'success' && schoolResponse.data) {
        // Teacher has a school - show "My School" and "Customize School" links
        sidebarItems.push({
          label: 'My School',
          href: ERoutes.MY_SCHOOL,
          icon: <Building2 className="w-5 h-5" />,
        });
        sidebarItems.push({
          label: 'Customize School',
          href: ERoutes.SCHOOL_CUSTOMIZATION,
          icon: <Settings className="w-5 h-5" />,
        });
      } else {
        // Teacher doesn't have a school - show "My School" for integrated setup experience
        sidebarItems.push({
          label: 'My School',
          href: ERoutes.MY_SCHOOL,
          icon: <Building2 className="w-5 h-5" />,
        });
      }
    } catch (error) {
      // On error, default to showing "My School" for integrated setup
      console.error('Error fetching school data for INDEPENDENT_TEACHER:', error);
      sidebarItems.push({
        label: 'My School',
        href: ERoutes.MY_SCHOOL,
        icon: <Building2 className="w-5 h-5" />,
      });
    }
  }

  // Determine if the sidebar should be shown
  const showSidebar = !currentPath.startsWith('/auth');

  // Check if this is an auth page
  const isAuthPage = currentPath.startsWith('/auth');


  return (
    <html suppressHydrationWarning lang="en" data-theme="edu">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <CustomProvider session={ssrSession}>
          {isAuthPage ? (
            // For auth and onboarding pages, render children directly without DashboardTemplate
            children
          ) : (
            // For all other pages, use DashboardTemplate
            <DashboardTemplate
              pageContainerType="contained"
              userMenuDropdownProps={{
                userName: ssrSession?.user?.name || '',
                userEmail: ssrSession?.user?.email || '',
              }}
              sidebarItems={showSidebar ? sidebarItems : []}
              schoolInfo={schoolInfo}
            >
              {children}
            </DashboardTemplate>
          )}
        </CustomProvider>
      </body>
    </html>
  );
}
